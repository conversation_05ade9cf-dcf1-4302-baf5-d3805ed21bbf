Gemini Frontend Clone Assignment -

Objective:
Build a fully functional, responsive, and visually appealing frontend for a Gemini-style
conversational AI chat application. The app should simulate OTP login, chatroom
management, AI messaging, image uploads, and a variety of modern UX/UI features.
This assignment is designed to evaluate your practical knowledge of React/Next.js,
component-based architecture, client-side state management, UX details, form validation,
and performance optimization.

Core Requirements
1. Authentication

● OTP-based Login/Signup flow using country codes.
● Fetch country data from an external API (e.g., restcountries.com) to show dial
codes.
● Simulate OTP send and validation using setTimeout.
● Validate form inputs using React Hook Form + Zod.

2. Dashboard

● List of user’s chatrooms

● Features to Create/Delete chatrooms
● Show confirmation via toast notifications

3. Chatroom Interface
● Chat UI with:
○ User and simulated AI messages
○ Timestamps
○ Typing indicator: "Gemini is typing..."
○ Fake AI reply after a delay using setTimeout
○ Throttling Gemini responses (simulate delayed AI thinking)
● Auto-scroll to latest message
● Reverse infinite scroll to fetch older messages (simulate with dummy data)
● Client-side pagination for messages (e.g., 20 per page)
● Support for image upload in chat (base64 or preview URL)
● Copy-to-clipboard feature on message hover

4. Global UX Features

● Mobile Responsive Design
● Dark Mode Toggle
● Debounced search bar to filter chatrooms by title
● Save auth & chat data using localStorage
● Display loading skeletons for chat messages
● Toast notifications for key actions (OTP sent, message sent, chatroom deleted, etc.)

● Keyboard accessibility for all main interactions

Technical Requirements:

Feature Tech Stack / Library

Framework :- Next.js 15 (App Router)

State Management - Zustand

Form Validation - React Hook Form + Zod

Styling - Tailwind CSS

Image Upload - Use local preview URL or
base64 (no backend needed)

Message Handling - Simulate AI response using
setTimeout with throttling

Notes

1. Source Code
○ Clean, well-organized project with modular components
○ Descriptive naming conventions and reusable hooks/components

2. README Documentation
Include in your GitHub:
○ Project overview and live link
○ Setup and run instructions
○ Folder/component structure explanation
○ How throttling, pagination, infinite scroll, and form validation are implemented
○ Screenshots (optional but encouraged)

Evaluation Criteria

● Functional completion and polish
● UI/UX quality on desktop and mobile
● Code readability, modularity, and structure
● Proper use of form validation and state management
● Implementation of chat behaviors (scroll, image upload, typing, toasts, copy, etc.)
● Responsiveness and accessibility
● Deployment and documentation completeness